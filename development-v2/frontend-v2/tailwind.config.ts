import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      // Design tokens from ChartGenius_TZ/tokens.chartgenius.json
      colors: {
        primary: {
          DEFAULT: "#34d399",
          foreground: "#0f172a",
        },
        secondary: {
          DEFAULT: "#60a5fa",
          foreground: "#0f172a",
        },
        neutral: {
          900: "#0d1117",
          800: "#1f2937",
          700: "#374151",
        },
        danger: {
          DEFAULT: "#f43f5e",
          foreground: "#fef2f2",
        },
        // Additional colors for crypto trading
        success: "#10b981",
        warning: "#f59e0b",
        long: "#10b981",
        short: "#ef4444",
        // Chart colors
        chart: {
          candle: {
            up: "#10b981",
            down: "#ef4444",
          },
          volume: "#6b7280",
          ma: {
            fast: "#3b82f6",
            slow: "#f59e0b",
          },
          rsi: {
            line: "#8b5cf6",
            overbought: "#ef4444",
            oversold: "#10b981",
          }
        }
      },
      borderRadius: {
        xs: "2px",  // TailwindCSS 4: renamed from sm
        sm: "4px",  // TailwindCSS 4: renamed from default
        md: "6px",
        lg: "8px",
      },
      spacing: {
        xs: "4px",
        sm: "8px",
        md: "16px",
        lg: "24px",
      },
      fontFamily: {
        base: ["Inter", "Roboto", "sans-serif"],
        mono: ["Menlo", "monospace"],
      },
      // Layout breakpoints for desktop-first design
      screens: {
        'desktop': '1280px',
        'mobile': {'max': '768px'},
        'tablet': {'min': '769px', 'max': '1279px'},
      },
      // Grid system for 12-column layout
      gridTemplateColumns: {
        '12': 'repeat(12, minmax(0, 1fr))',
        'sidebar-main': '280px 1fr',
        'chart-signals': '9fr 3fr',
      },
      // Animation for smooth transitions
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
      // Box shadows for depth - TailwindCSS 4: updated scale
      boxShadow: {
        'xs': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',  // TailwindCSS 4: renamed from sm
        'sm': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',  // TailwindCSS 4: renamed from default
        'card': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'card-hover': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'modal': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      }
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
    require('@tailwindcss/aspect-ratio'),
  ],
  // Dark mode configuration
  darkMode: 'class',
}

export default config
