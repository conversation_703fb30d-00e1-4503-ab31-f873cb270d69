@import "tailwindcss";

/* TailwindCSS 4 Theme Variables */
@theme {
  /* Light theme colors */
  --color-background: oklch(100% 0 0);
  --color-foreground: oklch(9% 0.026 285.75);
  --color-card: oklch(100% 0 0);
  --color-card-foreground: oklch(9% 0.026 285.75);
  --color-popover: oklch(100% 0 0);
  --color-popover-foreground: oklch(9% 0.026 285.75);
  --color-primary: oklch(70% 0.14 160);
  --color-primary-foreground: oklch(15% 0.026 285.75);
  --color-secondary: oklch(75% 0.12 220);
  --color-secondary-foreground: oklch(15% 0.026 285.75);
  --color-muted: oklch(96% 0.006 285.75);
  --color-muted-foreground: oklch(47% 0.013 285.75);
  --color-accent: oklch(96% 0.006 285.75);
  --color-accent-foreground: oklch(15% 0.026 285.75);
  --color-destructive: oklch(60% 0.15 25);
  --color-destructive-foreground: oklch(98% 0.006 285.75);
  --color-border: oklch(91% 0.006 285.75);
  --color-input: oklch(91% 0.006 285.75);
  --color-ring: oklch(70% 0.14 160);

  /* Crypto trading colors */
  --color-success: oklch(65% 0.15 160);
  --color-warning: oklch(75% 0.15 70);
  --color-long: oklch(65% 0.15 160);
  --color-short: oklch(60% 0.15 25);

  /* Chart colors */
  --color-chart-candle-up: oklch(65% 0.15 160);
  --color-chart-candle-down: oklch(60% 0.15 25);
  --color-chart-volume: oklch(50% 0.026 285.75);
  --color-chart-ma-fast: oklch(60% 0.15 250);
  --color-chart-ma-slow: oklch(75% 0.15 70);
  --color-chart-rsi-line: oklch(65% 0.15 300);
  --color-chart-rsi-overbought: oklch(60% 0.15 25);
  --color-chart-rsi-oversold: oklch(65% 0.15 160);

  /* Border radius */
  --radius: 0.5rem;
}

/* Dark mode theme */
.dark {
  --color-background: oklch(9% 0.026 285.75);
  --color-foreground: oklch(98% 0.006 285.75);
  --color-card: oklch(9% 0.026 285.75);
  --color-card-foreground: oklch(98% 0.006 285.75);
  --color-popover: oklch(9% 0.026 285.75);
  --color-popover-foreground: oklch(98% 0.006 285.75);
  --color-primary: oklch(70% 0.14 160);
  --color-primary-foreground: oklch(15% 0.026 285.75);
  --color-secondary: oklch(18% 0.026 285.75);
  --color-secondary-foreground: oklch(98% 0.006 285.75);
  --color-muted: oklch(18% 0.026 285.75);
  --color-muted-foreground: oklch(65% 0.013 285.75);
  --color-accent: oklch(18% 0.026 285.75);
  --color-accent-foreground: oklch(98% 0.006 285.75);
  --color-destructive: oklch(30% 0.15 25);
  --color-destructive-foreground: oklch(98% 0.006 285.75);
  --color-border: oklch(18% 0.026 285.75);
  --color-input: oklch(18% 0.026 285.75);
  --color-ring: oklch(70% 0.14 160);
}

/* Base styles */
@layer base {
  * {
    border-color: var(--color-border);
  }

  body {
    background-color: var(--color-background);
    color: var(--color-foreground);
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Focus styles - TailwindCSS 4: updated outline utilities */
  *:focus-visible {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px var(--color-ring), 0 0 0 4px var(--color-background);
  }

  /* Selection styles */
  ::selection {
    background-color: color-mix(in srgb, var(--color-primary) 20%, transparent);
  }
}

/* Component styles - TailwindCSS 4: using @utility instead of @layer components */
@utility btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

@utility btn:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px var(--color-ring), 0 0 0 4px var(--color-background);
}

@utility btn:disabled {
  opacity: 0.5;
  pointer-events: none;
}

@utility btn-primary {
  background-color: var(--color-primary);
  color: var(--color-primary-foreground);
}

@utility btn-primary:hover {
  background-color: color-mix(in srgb, var(--color-primary) 90%, black);
}

@utility btn-secondary {
  background-color: var(--color-secondary);
  color: var(--color-secondary-foreground);
}

@utility btn-secondary:hover {
  background-color: color-mix(in srgb, var(--color-secondary) 80%, black);
}

@utility btn-outline {
  border: 1px solid var(--color-input);
  background-color: var(--color-background);
}

@utility btn-outline:hover {
  background-color: var(--color-accent);
  color: var(--color-accent-foreground);
}

@utility btn-ghost:hover {
  background-color: var(--color-accent);
  color: var(--color-accent-foreground);
}

@utility btn-destructive {
  background-color: var(--color-destructive);
  color: var(--color-destructive-foreground);
}

@utility btn-destructive:hover {
  background-color: color-mix(in srgb, var(--color-destructive) 90%, black);
}
  
/* Size variants */
@utility btn-sm {
  height: 2.25rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

@utility btn-md {
  height: 2.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

@utility btn-lg {
  height: 2.75rem;
  padding-left: 2rem;
  padding-right: 2rem;
}

@utility btn-icon {
  height: 2.5rem;
  width: 2.5rem;
}

/* Card component */
@utility card {
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
  background-color: var(--color-card);
  color: var(--color-card-foreground);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

@utility card-header {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  padding: 1.5rem;
}

@utility card-title {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1;
  letter-spacing: -0.025em;
}

@utility card-description {
  font-size: 0.875rem;
  color: var(--color-muted-foreground);
}

@utility card-content {
  padding: 1.5rem;
  padding-top: 0;
}

@utility card-footer {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  padding-top: 0;
}
  
/* Input styles */
@utility input {
  display: flex;
  height: 2.5rem;
  width: 100%;
  border-radius: 0.375rem;
  border: 1px solid var(--color-input);
  background-color: var(--color-background);
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
}

@utility input::placeholder {
  color: var(--color-muted-foreground);
}

@utility input:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px var(--color-ring), 0 0 0 4px var(--color-background);
}

@utility input:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Chart container */
@utility chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: var(--color-card);
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
  overflow: hidden;
}

/* Trading signal styles */
@utility signal-long {
  color: var(--color-long);
  border-color: color-mix(in srgb, var(--color-long) 20%, transparent);
  background-color: color-mix(in srgb, var(--color-long) 10%, transparent);
}

@utility signal-short {
  color: var(--color-short);
  border-color: color-mix(in srgb, var(--color-short) 20%, transparent);
  background-color: color-mix(in srgb, var(--color-short) 10%, transparent);
}
  
/* Responsive grid */
@utility grid-responsive {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 1rem;
}

@media (min-width: 768px) {
  .grid-responsive {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1280px) {
  .grid-responsive {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Desktop layout */
@utility desktop-layout {
  display: none;
}

@media (min-width: 1280px) {
  .desktop-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    height: 100vh;
  }
}

/* Mobile layout */
@utility mobile-layout {
  display: flex;
  flex-direction: column;
}

/* Sidebar */
@utility sidebar {
  width: 280px;
  border-right: 1px solid var(--color-border);
  background-color: color-mix(in srgb, var(--color-card) 50%, transparent);
  backdrop-filter: blur(8px);
}

/* Header */
@utility header {
  height: 3.5rem;
  border-bottom: 1px solid var(--color-border);
  background-color: color-mix(in srgb, var(--color-background) 95%, transparent);
  backdrop-filter: blur(8px);
}

/* Main content area */
@utility main-content {
  flex: 1 1 0%;
  overflow: auto;
  padding: 1.5rem;
}

/* Chart grid */
@utility chart-grid {
  display: grid;
  gap: 1.5rem;
}

@media (min-width: 1280px) {
  .chart-grid {
    grid-template-columns: 9fr 3fr;
  }
}
  
/* Animation utilities */
@utility animate-in {
  animation: fadeIn 0.3s ease-in-out;
}

@utility animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

/* Loading states */
@utility loading-skeleton {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  background-color: var(--color-muted);
  border-radius: 0.375rem;
}

/* Telegram WebApp specific styles */
@utility twa-viewport {
  min-height: 100vh;
  background-color: var(--color-background);
}

/* Scrollbar styles */
@utility custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--color-muted) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: var(--color-muted);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-muted-foreground);
}
