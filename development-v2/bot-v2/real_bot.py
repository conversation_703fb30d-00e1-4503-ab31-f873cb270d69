#!/usr/bin/env python3
"""
ChartGenius v2 Telegram <PERSON> (aiogram 3.4)
Реализация согласно ТЗ R6/R7
"""

import asyncio
import logging
import aiohttp
from datetime import datetime
from typing import Dict, Any

from aiogram import <PERSON><PERSON>, Dispatcher, types
from aiogram.filters import Command
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton, WebAppInfo
from aiogram.utils.keyboard import InlineKeyboardBuilder

# Конфигурация
BOT_TOKEN = "7279183061:AAERodVAje0VnifJmUJWeq0EM4FxMueXrB0"
BACKEND_URL = "http://localhost:8001"
WEBAPP_URL = "http://localhost:3001"
ADMIN_TELEGRAM_ID = 299820674

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Инициализация бота и диспетчера
bot = Bot(token=BOT_TOKEN)
dp = Dispatcher()

async def get_backend_data(endpoint: str) -> Dict[str, Any]:
    """Получить данные от backend API"""
    url = f"{BACKEND_URL}{endpoint}"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {"error": f"Backend returned {response.status}"}
    except Exception as e:
        return {"error": f"Backend connection failed: {str(e)}"}

def get_user_role(user_id: int) -> str:
    """Определить роль пользователя"""
    if user_id == ADMIN_TELEGRAM_ID:
        return "admin"
    # TODO: Получать роль из базы данных
    return "premium"  # Для тестирования

@dp.message(Command("start"))
async def cmd_start(message: types.Message):
    """Команда /start - приветствие + кнопка «Открыть кабинет»"""
    
    # Создаем клавиатуру без WebApp для локального тестирования
    keyboard = InlineKeyboardBuilder()
    keyboard.add(InlineKeyboardButton(
        text="🌐 Открыть веб-кабинет",
        url="http://localhost:3001"  # Обычная ссылка для локального тестирования
    ))
    keyboard.add(InlineKeyboardButton(
        text="📊 Получить сигналы",
        callback_data="get_signals"
    ))
    keyboard.add(InlineKeyboardButton(
        text="💎 Тарифы",
        callback_data="show_plans"
    ))
    keyboard.adjust(1)  # По одной кнопке в ряд
    
    welcome_text = f"""
🚀 <b>ChartGenius v2</b>

Добро пожаловать, {message.from_user.first_name}!

<b>🎯 Ваши возможности:</b>
• AI-анализ криптовалют
• Торговые сигналы в реальном времени
• Технический анализ графиков
• Персональные рекомендации

<b>🌐 Веб-кабинет:</b> Полный функционал с графиками
<b>📱 Telegram:</b> Быстрый доступ к сигналам

<i>Нажмите "Открыть кабинет" для полного интерфейса</i>
    """
    
    await message.answer(
        welcome_text,
        reply_markup=keyboard.as_markup(),
        parse_mode="HTML"
    )

@dp.message(Command("help"))
async def cmd_help(message: types.Message):
    """Команда /help - справка"""
    
    help_text = """
📚 <b>Справка ChartGenius v2</b>

<b>🤖 Команды бота:</b>
/start - Главное меню
/help - Эта справка
/profile - Профиль и настройки
/plans - Тарифы и подписки
/signals - Торговые сигналы (Premium)

<b>🔧 Админ-команды:</b>
/admin - Панель администратора

<b>🌐 Веб-интерфейс:</b>
• Детальные графики
• Расширенный анализ
• История сигналов
• Настройки уведомлений

<b>💬 Поддержка:</b> @support_chartgenius
    """
    
    await message.answer(help_text, parse_mode="HTML")

@dp.message(Command("profile"))
async def cmd_profile(message: types.Message):
    """Команда /profile - Login Widget / WebApp"""
    
    keyboard = InlineKeyboardBuilder()
    keyboard.add(InlineKeyboardButton(
        text="🌐 Открыть профиль",
        url=f"{WEBAPP_URL}/profile"
    ))
    
    profile_text = f"""
👤 <b>Профиль пользователя</b>

<b>Имя:</b> {message.from_user.first_name}
<b>Username:</b> @{message.from_user.username or 'не указан'}
<b>ID:</b> {message.from_user.id}
<b>Статус:</b> {get_user_role(message.from_user.id).title()}

<i>Откройте веб-интерфейс для детальных настроек</i>
    """
    
    await message.answer(
        profile_text,
        reply_markup=keyboard.as_markup(),
        parse_mode="HTML"
    )

@dp.message(Command("plans"))
async def cmd_plans(message: types.Message):
    """Команда /plans - тарифы + оплата"""
    
    keyboard = InlineKeyboardBuilder()
    keyboard.add(InlineKeyboardButton(
        text="💎 Выбрать тариф",
        url=f"{WEBAPP_URL}/plans"
    ))
    
    plans_text = """
💎 <b>Тарифные планы ChartGenius</b>

<b>🆓 Free</b>
• Базовый анализ
• 3 сигнала в день
• Общие рекомендации

<b>⭐ Premium - $29/мес</b>
• Расширенный AI-анализ
• Неограниченные сигналы
• Персональные рекомендации
• Приоритетная поддержка

<b>💎 VIP - $99/мес</b>
• Все функции Premium
• Эксклюзивные стратегии
• Персональный менеджер
• Ранний доступ к новинкам

<i>Нажмите кнопку для оформления подписки</i>
    """
    
    await message.answer(
        plans_text,
        reply_markup=keyboard.as_markup(),
        parse_mode="HTML"
    )

@dp.message(Command("signals"))
async def cmd_signals(message: types.Message):
    """Команда /signals - последние 3 сигнала (premium, vip)"""
    
    user_role = get_user_role(message.from_user.id)
    
    if user_role not in ["premium", "vip", "admin"]:
        keyboard = InlineKeyboardBuilder()
        keyboard.add(InlineKeyboardButton(
            text="💎 Получить Premium",
            callback_data="upgrade_premium"
        ))
        
        await message.answer(
            "🔒 <b>Доступ ограничен</b>\n\nТорговые сигналы доступны только для Premium и VIP пользователей.",
            reply_markup=keyboard.as_markup(),
            parse_mode="HTML"
        )
        return
    
    # Получаем сигналы от backend
    signals_data = await get_backend_data("/api/v2/signals/")
    
    if "error" in signals_data:
        await message.answer(f"❌ Ошибка получения сигналов: {signals_data['error']}")
        return
    
    signals = signals_data.get("signals", [])[:3]  # Последние 3 сигнала
    
    if not signals:
        await message.answer("📊 <b>Активных сигналов нет</b>\n\nПроверьте позже или откройте веб-кабинет для истории.", parse_mode="HTML")
        return
    
    signals_text = "📊 <b>Последние торговые сигналы</b>\n\n"
    
    for i, signal in enumerate(signals, 1):
        signal_type = signal.get("signal_type", "UNKNOWN")
        symbol = signal.get("symbol", "UNKNOWN")
        confidence = signal.get("confidence", 0) * 100
        entry_price = signal.get("entry_price", 0)
        
        emoji = "🟢" if signal_type == "LONG" else "🔴" if signal_type == "SHORT" else "⚪"
        
        signals_text += f"{emoji} <b>{i}. {symbol}</b> - {signal_type}\n"
        signals_text += f"💰 Вход: ${entry_price:,.2f}\n"
        signals_text += f"📈 Уверенность: {confidence:.1f}%\n"
        signals_text += f"⏰ {signal.get('timeframe', '1h')}\n\n"
    
    signals_text += f"🕐 Обновлено: {datetime.now().strftime('%H:%M:%S')}"
    
    keyboard = InlineKeyboardBuilder()
    keyboard.add(InlineKeyboardButton(
        text="📈 Открыть графики",
        web_app=WebAppInfo(url=f"{WEBAPP_URL}/analysis")
    ))
    
    await message.answer(
        signals_text,
        reply_markup=keyboard.as_markup(),
        parse_mode="HTML"
    )

@dp.message(Command("admin"))
async def cmd_admin(message: types.Message):
    """Команда /admin - меню Users·Prompts·LLM·Broadcast (только для админов)"""
    
    if message.from_user.id != ADMIN_TELEGRAM_ID:
        await message.answer("🔒 Доступ запрещен. Команда только для администраторов.")
        return
    
    keyboard = InlineKeyboardBuilder()
    keyboard.add(InlineKeyboardButton(
        text="👥 Пользователи",
        callback_data="admin_users"
    ))
    keyboard.add(InlineKeyboardButton(
        text="📝 Промпты",
        callback_data="admin_prompts"
    ))
    keyboard.add(InlineKeyboardButton(
        text="🤖 LLM",
        callback_data="admin_llm"
    ))
    keyboard.add(InlineKeyboardButton(
        text="📢 Рассылка",
        callback_data="admin_broadcast"
    ))
    keyboard.add(InlineKeyboardButton(
        text="🌐 Веб-панель",
        web_app=WebAppInfo(url=f"{WEBAPP_URL}/admin")
    ))
    keyboard.adjust(2, 2, 1)  # 2-2-1 кнопки в рядах
    
    admin_text = f"""
🔧 <b>Панель администратора</b>

<b>Добро пожаловать, {message.from_user.first_name}!</b>

<b>📊 Быстрая статистика:</b>
• Активных пользователей: загрузка...
• Сигналов сегодня: загрузка...
• Статус системы: ✅ Работает

<i>Выберите раздел для управления</i>
    """
    
    await message.answer(
        admin_text,
        reply_markup=keyboard.as_markup(),
        parse_mode="HTML"
    )

# Обработчики callback-запросов
@dp.callback_query()
async def handle_callback(callback: types.CallbackQuery):
    """Обработка нажатий на inline-кнопки"""
    
    if callback.data == "get_signals":
        # Перенаправляем на команду /signals
        await cmd_signals(callback.message)
    
    elif callback.data == "show_plans":
        # Перенаправляем на команду /plans
        await cmd_plans(callback.message)
    
    elif callback.data == "upgrade_premium":
        keyboard = InlineKeyboardBuilder()
        keyboard.add(InlineKeyboardButton(
            text="💎 Оформить Premium",
            web_app=WebAppInfo(url=f"{WEBAPP_URL}/plans")
        ))
        
        await callback.message.answer(
            "💎 <b>Переход на Premium</b>\n\nОткройте веб-интерфейс для оформления подписки с безопасной оплатой.",
            reply_markup=keyboard.as_markup(),
            parse_mode="HTML"
        )
    
    elif callback.data.startswith("admin_"):
        if callback.from_user.id != ADMIN_TELEGRAM_ID:
            await callback.answer("🔒 Доступ запрещен", show_alert=True)
            return
        
        section = callback.data.replace("admin_", "")
        await callback.message.answer(f"🔧 Раздел '{section}' в разработке. Используйте веб-панель.")
    
    await callback.answer()

async def main():
    """Главная функция запуска бота"""
    
    logger.info("🚀 Запуск ChartGenius v2 Bot (aiogram 3.4)")
    logger.info(f"🔗 Backend URL: {BACKEND_URL}")
    logger.info(f"🌐 WebApp URL: {WEBAPP_URL}")
    
    # Проверяем подключение к backend
    health_data = await get_backend_data("/health")
    if "error" in health_data:
        logger.error(f"❌ Backend недоступен: {health_data['error']}")
    else:
        logger.info(f"✅ Backend подключен: {health_data.get('status')}")
    
    # Запускаем бота
    try:
        await dp.start_polling(bot)
    except KeyboardInterrupt:
        logger.info("🛑 Бот остановлен пользователем")
    finally:
        await bot.session.close()

if __name__ == "__main__":
    asyncio.run(main())
